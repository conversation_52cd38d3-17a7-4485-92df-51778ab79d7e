import { BrowserRouter, Routes, Route } from "react-router-dom";
import HomePage from "./page/HomePage";
import AboutPage from "./page/AboutPage";
import NavBar from "./components/NavBar";
import Footer from "./components/Footer";
import FAQPage from "./page/FAQPage";
import ContactPage from "./page/ContactPage";
import TermUsePage from "./page/TermUsePage";
import PrivacyPages from "./page/PrivacyPages";
import CareerPage from "./page/CareerPage";
import TermAndConditionPage from "./page/TermAndConditionPage";
import { useEffect, useState } from "react";

const App = () => {
  const [isTop, setIsTop] = useState(true);
  useEffect(() => {
    const onScroll = () => {
      setIsTop(window.pageYOffset === 0);
    };

    window.addEventListener("scroll", onScroll);

    return () => {
      window.removeEventListener("scroll", onScroll);
    };
  }, []);
  useEffect(() => {
    const onScroll = () => {
      setIsTop(window.pageYOffset === 0);
    };

    window.addEventListener("scroll", onScroll);

    return () => {
      window.removeEventListener("scroll", onScroll);
    };
  }, []);
  const scrollToTop = () => {
    window.scrollTo({
      top: isTop ? document.body.scrollHeight : 0,
      behavior: "smooth",
    });
  };
  return (
    <BrowserRouter>
      <NavBar />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/faq" element={<FAQPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/careers" element={<CareerPage />} />
        <Route path="/privacy-policy" element={<PrivacyPages />} />
        <Route path="/term-use" element={<TermUsePage />} />
        <Route
          path="/terms-and-conditions"
          element={<TermAndConditionPage />}
        />
      </Routes>
      <button
        onClick={scrollToTop}
        className="fixed z-40 bottom-8 right-8 bg-purple-600 hover:bg-purple-700 outline-none text-white p-3 rounded-full shadow-lg transition-all duration-300"
        aria-label="Scroll to top"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={isTop ? "M5 14l7 7m0 0l7-7m-7 7V2" : "M5 10l7-7m0 0l7 7m-7-7v18"}
          />
        </svg>
      </button>
      <Footer />
    </BrowserRouter>
  );
};

export default App;
