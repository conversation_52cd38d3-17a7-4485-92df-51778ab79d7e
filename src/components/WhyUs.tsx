import whyIcons from "../assets/why-icon.svg";
import ImageWhy from "../assets/banner-3.webp";

const WhyChooseUs = () => {
  const advantages = [
    {
      text: "You can save more with friends, families, colleagues and others without the hassle of calling and not trusting one another.",
    },
    {
      text: "You can get the down payment you need for a house, your rent or a car without the stress of a bank's loan.",
    },
    {
      text: "You can get quick cash for house furnitures, emergency funds or capital-intensive purchases without paying any interest.",
    },
    {
      text: "You can get cash for investment purposes and any other business related expenses to kick start your dream.",
    },
  ];

 
return (
  <div className="bg-[#EDE8F1] min-h-screen mt-10 md:mt-20" id="why">
    <div className="w-full px-4 sm:px-8 lg:px-16 mx-auto py-12 sm:py-16">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-20 place-items-center">
        <div className="space-y-6 sm:space-y-8">
          <div className="space-y-3">
            <article className="flex justify-center sm:justify-start items-center gap-2">
              <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454]"></div>ADVANTAGES
              <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454] block sm:hidden"></div>
            </article>
            <h2 className="text-3xl sm:text-4xl lg:text-[44px] font-bold sm:text-left text-center">Why choose Equalcash?</h2>
          </div>

          <div className="space-y-4 sm:space-y-6">
            {advantages.map((advantage, index) => (
              <div key={index} className="flex items-start space-x-3 sm:space-x-4">
                <img src={whyIcons} alt="icon" className="w-5 sm:w-auto" />
                <p className="text-sm sm:text-base text-gray-700 leading-relaxed">
                  {advantage.text}
                </p>
              </div>
            ))}
          </div>
        </div>

        <img 
          src={ImageWhy} 
          alt="why-image" 
          className="w-full h-auto max-w-[400px] sm:max-w-[500px] mx-auto"
        />
      </div>
    </div>
  </div>
);

};

export default WhyChooseUs;
