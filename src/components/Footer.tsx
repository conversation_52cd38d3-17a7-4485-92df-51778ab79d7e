import { Facebook, Twitter, Instagram, Youtube } from "lucide-react";
import Appstore from "../assets/appstore.png";
import Playstore from "../assets/ playstore.png";
import { Link } from "react-router-dom";

const Footer = () => {
  const footerLinks = {
    company: [
      { label: "About Us", href: "/about" },
      { label: "Contact", href: "/contact" },
      { label: "Careers", href: "/careers" },
      { label: "FAQ", href: "/faq" },
    ],
    explore: [
      { label: "How it works", href: "#how" },
      { label: "Why Equalcash", href: "#why" },
      { label: "Testimonials", href: "#testimonials" },
    ],
    legal: [
      { label: "Privacy Policy", href: "/privacy-policy" },
      { label: "Terms of use", href: "/term-use" },
      { label: "Terms & Conditions", href: "/terms-and-conditions" },
    ],
  };

  return (
    <footer className="bg-[#411567] text-white min-h-[700px] p-4 sm:p-6 lg:p-10 relative overflow-hidden">
      <div className="w-full px-8 mx-auto pt-10 lg:pt-20">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-6 lg:gap-8 mb-16">
          <div className="col-span-2 sm:col-span-3 lg:col-span-2 space-y-4 lg:space-y-6">
            <div className="text-xl sm:text-2xl font-bold">Equalcash</div>
            <p className="text-purple-200 text-xs sm:text-sm w-[250px]">
              Equal Cash Technology Incorporated is a financial technology
              company, a Delaware Incorporated Company in the United Staes of
              America and a Limited Liability Company in Nigeria.
            </p>
            <div className="space-y-2">
              <div className="text-sm font-semibold">Download Mobile App</div>
              <div className="flex flex-col sm:flex-row gap-2 sm:space-x-4">
                <img src={Appstore} alt="app store" className="w-32 h-10" />
                <img src={Playstore} alt="play store" className="w-32 h-10" />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Company</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.href}
                    className="text-purple-200 hover:text-white text-sm"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Explore</h3>
            <ul className="space-y-3">
              {footerLinks.explore.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-purple-200 hover:text-white text-sm"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Legal</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.href}
                    className="text-purple-200 hover:text-white text-sm"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="col-span-2 sm:col-span-3 lg:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Connect with us</h3>
            <ul className="space-y-3">
              <li className="text-purple-200 text-xs sm:text-sm">
                <EMAIL>.
              </li>
              <li className="text-purple-200 text-xs sm:text-sm">
                +1-502-408-5015
              </li>
              <li className="text-purple-200 text-xs sm:text-sm">
                6709 Green Haven Road,
                <br />
                Lanham, MD, 20706, United States.
              </li>
              <li className="flex space-x-4 pt-2">
                <a href="#" className="text-purple-200 hover:text-white">
                  <Facebook className="w-5 h-5" />
                </a>
                <a href="#" className="text-purple-200 hover:text-white">
                  <Twitter className="w-5 h-5" />
                </a>
                <a href="#" className="text-purple-200 hover:text-white">
                  <Instagram className="w-5 h-5" />
                </a>
                <a href="#" className="text-purple-200 hover:text-white">
                  <Youtube className="w-5 h-5" />
                </a>
              </li>
            </ul>
          </div>
        </div>

        <section className="flex items-center justify-center gap-4 px-4">
          <div className="flex-1 border-t border-purple-800"></div>
          <div className="text-center text-purple-200 text-xs sm:text-sm whitespace-nowrap px-2">
            Copyright © 2025 - Equalcash Solutions. All rights reserved.
          </div>
          <div className="flex-1 border-t border-purple-800"></div>
        </section>
        <div className="hidden lg:block absolute -bottom-16 left-0 right-0">
          <div className="text-transparent bg-clip-text bg-gradient-to-t from-[#33105100] via-[#311251DE] to-[#411567] text-[100px] sm:text-[150px] lg:text-[200px] font-bold w-full text-center overflow-hidden">
            Equalcash
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
