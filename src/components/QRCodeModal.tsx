import { X } from 'lucide-react';
import QrcodeImage from '../assets/qr-code2.png'
import GIcons from '../assets/gicon.png'

interface QRDownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const QRDownloadModal = ({ isOpen, onClose }: QRDownloadModalProps) => {
  if (!isOpen) return null;

  return (
  
   <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4">
   <div className="bg-white rounded-3xl w-full max-w-lg relative py-8 sm:py-10 px-4 sm:px-6">
     <button 
       onClick={onClose}
       className="absolute outline-none right-3 top-3 sm:right-4 cursor-pointer sm:top-4 text-gray-500 hover:text-gray-700"
     >
       <X size={20} className="sm:w-6 sm:h-6" />
     </button>

     <div className="flex justify-center space-x-4 mb-4">
       <img 
         src={GIcons} 
         alt="Apple icon"
         
       />
     </div>

     <h2 className="text-center text-base sm:text-lg font-semibold mb-2">
       Scan to download
     </h2>

     <p className="text-center text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6">
       Position your phone camera within the frame
       <br />and follow the link to download.
     </p>

     <div className="flex justify-center mb-4">
       <div className="w-36 h-36 sm:w-48 sm:h-48 bg-gray-100 flex items-center justify-center">
         <img 
           src={QrcodeImage}
           alt="QR Code" 
           className="w-32 h-32 sm:w-40 sm:h-40"
         />
       </div>
     </div>

     <p className="text-center text-xs sm:text-sm">
       Scan the QR Code to download the{' '}<br />
       <span className="text-purple-600">Equalcash App</span>
     </p>
   </div>
 </div>

  );
};

export default QRDownloadModal;