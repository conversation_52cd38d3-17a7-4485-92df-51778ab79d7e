import { useState } from "react";
import { HiOutlineArrowDown } from "react-icons/hi";
interface FAQ {
  question: string;
  answer: string;
}

interface FAQSectionProps {
  data: FAQ[];
  subtitle?: boolean;
}

const FAQSection = ({ data, subtitle = true }: FAQSectionProps) => {
  const [openIndex, setOpenIndex] = useState(0);


return (
  <div className="bg-[#EDE8F1] min-h-screen p-4 sm:p-6 lg:p-8">
    <div className="max-w-4xl mx-auto">
      <div className="space-y-4 sm:space-y-6">
        <div className="space-y-2 text-center mb-12 sm:mb-16 lg:mb-20">
          {subtitle && (
            <article className="flex justify-center items-center gap-2">
              <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454]"></div>FAQs
              <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454]"></div>
            </article>
          )}
          <h2 className="text-3xl sm:text-4xl lg:text-[44px] font-bold">
            Frequently Asked Questions
          </h2>
          <p className="text-gray-600 text-sm sm:text-base">
            Got Questions? Get answers and advice from the Equalcash Team
          </p>
        </div>

        <div className="space-y-3 sm:space-y-4">
          {data?.map((faq, index) => (
            <div
              key={index}
              className="bg-white border border-gray-300 overflow-hidden"
            >
              <button
                onClick={() => setOpenIndex(openIndex === index ? -1 : index)}
                className="w-full outline-none px-4 sm:px-6 py-3 sm:py-4 text-left flex items-center justify-between"
              >
                <span className="font-medium text-sm sm:text-base">{faq.question}</span>
                <HiOutlineArrowDown
                  className={`w-4 h-4 sm:w-5 sm:h-5 text-blue-400 transition-transform ${
                    openIndex === index ? "transform rotate-180" : ""
                  }`}
                />
              </button>

              <div
                className={`px-4 sm:px-6 overflow-hidden transition-all ${
                  openIndex === index ? "py-3 sm:py-4" : "max-h-0"
                }`}
              >
               <p 
                  className="text-[#000B2380] text-sm sm:text-base"
                  dangerouslySetInnerHTML={{ __html: faq.answer.replace(/\n/g, '<br />') }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="text-center my-6 sm:my-8">
        <p className="text-sm sm:text-base">
          Have more questions? Check our{" "}
          <a href="/faq" className="text-[#471771]">FAQ page</a>
        </p>
      </div>
    </div>
  </div>
);

};

export default FAQSection;
