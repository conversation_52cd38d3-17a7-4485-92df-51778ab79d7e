import DownloadImage from "../assets/downloadApp.png";
import Barcode from "../assets/qr-code.png";

const DownloadApp = () => {
  return (
    <div className="bg-[#EDE8F1] min-h-[40vh] py-10 px-4 sm:px-8 lg:px-16 lg:py-32 relative">
      <div className="max-w-7xl mx-auto">
        <div className="bg-[#411567] rounded-3xl sm:rounded-[50px] p-8 sm:p-12 lg:p-16 h-full lg:h-[550px] overflow-hidden">
          <div className="flex flex-col lg:flex-row gap-8 items-center">
            <div className="flex-1 space-y-6 z-10">
              <h2 className="text-white text-2xl sm:text-3xl lg:text-4xl font-bold">
                Download our mobile App
              </h2>
              <h3 className="text-white text-xl sm:text-2xl lg:text-3xl font-bold leading-snug">
                Enjoy the best financial services from us
              </h3>
              <p className="text-purple-100 text-sm sm:text-base max-w-lg">
                We have seen that people who save alone don't usually meet this
                target. But with EqualCash, you save with commitment with
                friends, families, colleagues and others to meet your target
              </p>

              <div className="flex items-center gap-4 mt-8">
                <img
                  src={Barcode}
                  alt="QR code for app download"
                  className="w-28 sm:w-32"
                />
                <p className="text-white text-sm sm:text-base max-w-xs">
                  Scan the QR code to download our App from Play & App store
                </p>
              </div>
            </div>
            <div className="hidden lg:block absolute bottom-32 right-0 sm:right-8 lg:right-36 w-48 sm:w-64 lg:w-auto">
           <img src={DownloadImage} alt="download-image" className="w-full h-auto" />
         </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadApp;
