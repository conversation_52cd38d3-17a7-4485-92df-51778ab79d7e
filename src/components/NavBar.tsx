import { useState } from "react";
import { FaBars, FaTimes } from "react-icons/fa";
import Logo from "../assets/equalcash-logo.svg";
import { Link, NavLink } from "react-router-dom";
import QRDownloadModal from "./QRCodeModal";

export default function NavBar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <nav className="px-4 sm:px-16 py-6 flex justify-between items-center border-b-2 bg-white border-white">
        <div className="flex items-center">
          <Link to="/" className="">
            <img src={Logo} alt="logo" className="w-32 sm:w-auto" />{" "}
          </Link>
        </div>
        <div className="hidden md:flex font-medium items-center space-x-6">
          <NavLink
            to="/"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            Home
          </NavLink>
          <NavLink
            to="/about"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            About
          </NavLink>
          <NavLink
            to="/faq"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            FAQ
          </NavLink>
          <NavLink
            to="/careers"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            Careers
          </NavLink>
          <NavLink
            to="/contact"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            Contact
          </NavLink>
        </div>
        <button
          className="hidden md:block bg-[#4B1C74] font-bold text-white px-4 py-2 cursor-pointer rounded-md hover:bg-purple-800"
          onClick={() => setIsOpen(true)}
        >
          Download App
        </button>
        <button
          className="md:hidden text-[#4B1C74]"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
        </button>
      </nav>
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white px-4 py-6 space-y-4">
          <NavLink
            to="/"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            Home
          </NavLink>
          <NavLink
            to="/about"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            About
          </NavLink>
          <NavLink
            to="/faq"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            FAQ
          </NavLink>
          <NavLink
            to="/careers"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            Careers
          </NavLink>
          <NavLink
            to="/contact"
            className={({ isActive }) =>
              `block text-gray-600 hover:text-purple-800 ${
                isActive ? "text-purple-800 font-bold" : ""
              }`
            }
          >
            Contact
          </NavLink>
          <button
            className="w-full bg-[#4B1C74] text-white px-4 py-2 font-bold cursor-pointer rounded-md hover:bg-purple-800"
            onClick={() => setIsOpen(true)}
          >
            Download App
          </button>
        </div>
      )}
      <QRDownloadModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
}
