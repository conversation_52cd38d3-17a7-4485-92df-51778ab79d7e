import { useState } from "react";
import { MoveLeft, MoveRight } from "lucide-react";
import user from "../assets/user.png";

const TestimonialSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const testimonials = [
    {
      text: "I have no doubt that Equalcash App will change how people save and most especially meet their financial goals. It will significantly reduce the stress associated with the coordinating traditional saving schemes as we know it today.",
      name: "Dr. <PERSON>",
      title: "Founder/CEO",
    },
    {
      text: "I am presently involved in more than two traditional saving groups. I have seen firsthand the amount of calling and following up that we have to do in order to make sure we get our monthly payment. I can't wait to starting using Equalcash App.",
      name: "Mrs <PERSON>",
      title: "Head of Expansion and Business Intelligence",
    },
    {
      text: "The amount of work and due diligence that has gone into building Equalcash App is on another level. I am confident that people will see and appreciate it's multi-dimensional function.",
      name: "<PERSON><PERSON> <PERSON><PERSON><PERSON>",
      title: "Head of Legal and Compliance",
    },
    {
      text: 'The 3-in-1 offering of Equalcash App is truly unique. If you are "first-in-line", that\'s a zero interest loan. If you are "last-in-line", that\'s a solid saving or downpayment for something special for you. It is going to be life changing for its customers!',
      name: "Mr. Biodun Shoyombo",
      title: "Head, Infrastructure and Software",
    },
    // {
    //   text: "Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.",
    //   name: "Donald Douglass",
    //   title: "Additional Cooperation",
    // },
    // {
    //   text: "Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.",
    //   name: "Donald Douglass",
    //   title: "Additional Cooperation",
    // },
    // {
    //   text: "Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.",
    //   name: "Donald Douglass",
    //   title: "Additional Cooperation",
    // },
    // {
    //   text: "Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.Embark on a journey of learning, study and discovery with our innovative learning platform designed to empower and inspire you. Embark on a journey of learning, study and discovery.",
    //   name: "Donald Douglass",
    //   title: "Additional Cooperation",
    // },
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % testimonials.length);
  };

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  return (
    <div
      className="bg-[#EDE8F1] min-h-screen p-4 mt-20 sm:mt-28 lg:mt-44"
      id="testimonials"
    >
      <div className="w-full">
        <div className="mb-8 sm:mb-10 lg:mb-12 px-2 sm:px-8 lg:px-14">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 sm:gap-4">
            <div className="space-y-3 sm:space-y-4">
              <article className="flex justify-center sm:justify-start items-center gap-2">
                <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454] font-normal"></div>
                TESTIMONIAL
                <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454] block sm:hidden"></div>
              </article>
              <h2 className="text-3xl sm:text-4xl lg:text-[44px] font-bold sm:text-left text-center">
                What People Say About Us
              </h2>
              <p className="text-gray-600 text-sm sm:text-base max-w-2xl">
                Explore the inspiring stories and feedback from clients who've
                achieved their financials goals with our support.
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={prevSlide}
                className="border-none outline-none cursor-pointer hover:font-semibold text-[#471771] capitalize transition-colors flex items-center gap-2 text-sm sm:text-base"
              >
                <MoveLeft className="w-4 h-4 sm:w-5 sm:h-5" />
                <span>PREVIOUS</span>
              </button>
              <button
                onClick={nextSlide}
                className="hover:font-semibold outline-none border-none cursor-pointer text-[#471771] capitalize transition-colors flex items-center gap-2 text-sm sm:text-base"
              >
                <span>NEXT</span>
                <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>
        </div>

        <div className="overflow-x-scroll scrollbar-hide">
          <div
            className="flex transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(calc(${currentSlide * -100}% + ${
                window.innerWidth > 640 ? "2rem" : "0"
              }))`,
              width: `calc(80% + 2rem)`,
            }}
          >
            {testimonials.map((_testimonial, index) => (
              <div key={index} className="min-w-full p-2 sm:p-4">
                <div
                  className="grid grid-cols-1 grid-rows-1 lg:grid-cols-2 gap-2 sm:gap-4 w-full"
                  key={index}
                >
                  {[0, 1].map((offset) => {
                    const itemIndex = (index + offset) % testimonials.length;
                    return (
                      <div
                        key={offset}
                        className="bg-white p-4 rounded-2xl shadow-sm border border-gray-300 w-full h-full"
                      >
                        <p className="text-[#545454] mb-8 leading-relaxed text-sm sm:text-base">
                          {testimonials[itemIndex].text}
                        </p>
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-16 bg-gray-200 rounded-full">
                            <img src={user} alt="user" />
                          </div>
                          <div>
                            <div className="font-semibold text-base">
                              {testimonials[itemIndex].name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {testimonials[itemIndex].title}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialSlider;
