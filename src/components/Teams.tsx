import <PERSON> from "../assets/team-1.png";
import <PERSON> from "../assets/team-2.png";
import <PERSON> from "../assets/team-3.png";
import <PERSON><PERSON><PERSON> from "../assets/team-6.png";
import Tunde from "../assets/team-5.png";

const TeamProfiles = () => {
  const teamMembers = [
    {
      name: "Dr. <PERSON><PERSON><PERSON>",
      title: "Founder/CEO",
      bio: "<PERSON><PERSON><PERSON> holds a PhD in Financial Technology and Financial Inclusion. Prior to starting EqualCash Technology in 2023, he was an Investment and a Commercial Banker with vast experience in Nigeria, South Africa and the United States.",
      img: <PERSON>,
    },
    {
      name: "Mrs. <PERSON><PERSON>",
      title: "Executive Director",
      bio: "<PERSON><PERSON><PERSON> has a medical background but has a keen interest in financial technology. She leads our strategy team and works closely with the Head of Expansion and Business Intelligence.",
      img: <PERSON>,
    },
    {
      name: "Dr. <PERSON><PERSON><PERSON>",
      title: "Head of Legal and Compliance",
      bio: "<PERSON><PERSON><PERSON> is a legal practitioner with a decade of experience in public, international trade and commercial Law - an area in which holds LLB, LLM and PhD degrees respectively. He is also a research fellow whose interests over the year has spanned international trade, international finance, banking and African development, all within a law and development framework. ",
      // img: <PERSON>,
      img: <PERSON>,
    },
    {
      name: "Mrs. Adetoro Dolapo",
      title: "Head of Expansion and Business Intelligence.",
      bio: "Dolapo holds an MBA from the Institute of Management Development (IMD) in Switzerland, where she was awarded the prestigious IMD MBA Merit Scholarship. She also earned an MSc in Work Psychology and Business from Aston Business School in the UK and a BSc in Psychology from the University of Reading. ",
      img: Adetoro,
    },
    {
      name: "Engineer Tunde Olugbekan",
      title: "Head, Infrastructure and Software.",
      bio: "Tunde is a seasoned software engineer with over 20 years in the tech space. He also has experience in developing applications for the manufacturing industry and banking industries.",
      img: Michael,
    },
  ];

  return (
    <div className="bg-[#EDE8F1] p-4">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teamMembers.map((member, index) => (
            <div
              key={index}
              className="bg-white rounded-lg overflow-hidden shadow-sm p-3"
            >
              <div className="bg-[#179FA9] relative h-[370px]">
                <img
                  src={member.img}
                  alt={member.name}
                  className="w-full h-full bg-cover"
                />
              </div>

              <div className="p-2">
                <h3 className="text-lg font-semibold mb-1">{member.name}</h3>
                <div className="text-gray-600 text-sm mb-4">{member.title}</div>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {member.bio}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TeamProfiles;
