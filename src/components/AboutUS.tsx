import AboutImage from "../assets/image-5.png";

const AboutUsSection = () => {
  const stats = [
    { number: "1k+", label: "Active users" },
    { number: "100+", label: "Companies" },
    { number: "50+", label: "Reviews" },
  ];

  return (
    <div className="w-full bg-[#EDE8F1] mt-20 sm:mt-28 md:mt-36">
      <div className="max-w-full mx-auto px-4 sm:px-8 md:px-16">
        <div className="bg-[#411567] rounded-[40px] sm:rounded-[60px] md:rounded-[80px] p-6 sm:p-12 md:p-20 relative overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 items-center">
            <div className="space-y-6 md:space-y-8">
              <div className="space-y-2 md:space-y-3">
                <div className="flex items-center gap-3 md:gap-4">
                  <div className="w-16 sm:w-20 md:w-24 h-[2px] bg-[#FFFFFF80]"></div>
                  <div className="text-purple-300 text-xs sm:text-sm uppercase tracking-wider">
                    ABOUT US
                  </div>
                </div>

                <h2 className="text-white text-2xl sm:text-3xl md:text-[44px] font-bold leading-tight">
                  Little story about us
                </h2>
              </div>

              <p className="text-purple-200 text-sm sm:text-base leading-relaxed">
                Equal Cash is a form of unique and combined peer-to-peer saving
                platform. Equal Cash Technology APP is simply the automation of
                the “Ajo” or “Esusu” commonly practiced in Nigeria and other
                Sub-Saharan Africa and rotating savings and credit association -
                “ROSCA” in East Africa...
              </p>

              <div className="grid grid-cols-3 gap-2 sm:gap-3 md:gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="space-y-1 md:space-y-2">
                    <div className="text-white text-xl sm:text-2xl md:text-3xl font-medium">
                      {stat.number}
                    </div>
                    <div className="text-purple-300 text-xs sm:text-sm">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="hidden lg:block absolute right-0 top-1/2 -translate-y-1/2">
              <img src={AboutImage} alt="about" className="max-w-full h-auto" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutUsSection;
