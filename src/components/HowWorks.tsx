import { Download, UserCircle2, Building2, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import Banner from "../assets/banner-2.webp";
import AboutImage3 from "../assets/about-3.webp";
const HowItWorks = () => {
  const steps = [
    {
      icon: <Download className="w-8 h-8" />,
      title: "Download Equalcash App",
      description:
        "First you need to download the Equalcash App on google play store or apple app store on your mobile phone",
    },
    {
      icon: <UserCircle2 className="w-8 h-8" />,
      title: "Create Account",
      description:
        "Provide your personal details in order to create an Equalcash account. Don't worry! Your information is safe. ",
    },
    {
      icon: <FileCheck className="w-8 h-8" />,
      title: "Complete KYC",
      description:
        "Verify your account by completing the KYC, which includes a selfie and a valid ID card.",
    },
    {
      icon: <Building2 className="w-8 h-8" />,
      title: "Create/Join a company",
      description:
        "You can then create a saving company with your friends and family or join an existing saving company",
    },
  ];

  return (
    <div className="bg-[#EDE8F1] min-h-screen mt-10 md:mt-32" id="why">
      <div className="max-w-full px-4 sm:px-10 lg:px-16 mx-auto py-12 sm:py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-20 place-content-center">
          <img
            src={Banner}
            alt="banner"
            className="w-full h-auto max-w-[400px] sm:max-w-[500px] mx-auto order-2 sm:order-1"
          />
          <div className=" space-y-24 sm:space-y-8 order-1 sm:order-2">
            <div className="space-y-3">
              <article className="flex justify-center sm:justify-start items-center gap-2">
                <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454]"></div>
                OUR PROCESS
                <div className="w-16 sm:w-20 lg:w-24 h-[2px] bg-[#545454] block sm:hidden"></div>
              </article>
              <h2 className="text-3xl sm:text-4xl lg:text-[44px] font-bold sm:text-left text-center">
                How it works
              </h2>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 md:gap-8">
              {steps.map((step, index) => (
                <div key={index} className="">
                  <div className="bg-[#C6B7D3] w-16 h-16 rounded-full shadow-2xl flex items-center justify-center mb-2 sm:mb-3 md:mb-4 mx-auto sm:mx-0">
                    {step.icon}
                  </div>
                  <h3 className="text-base md:text-lg font-semibold mb-1 sm:mb-2 text-center sm:text-left">
                    {step.title}
                  </h3>
                  <p className="text-sm md:text-base text-[#545454] text-center sm:text-left">
                    {step.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="w-full bg-[#EDE8F1] mt-20 sm:mt-28 md:mt-36">
        <div className="max-w-full mx-auto px-4 sm:px-8 md:px-16">
          <div className="bg-[#411567] max-w-full rounded-[40px] sm:rounded-[60px] p-6 md:p-8 relative overflow-hidden h-auto md:h-[500px]">
            <div className="flex flex-col lg:flex-row gap-8 md:gap-12 lg:gap-24 items-center h-full">
              <div className="w-full lg:w-1/2 h-[300px] md:h-full">
                <img
                  src={AboutImage3}
                  alt="about"
                  className="w-full h-full object-cover rounded-[30px]"
                />
              </div>
              <div className="w-full lg:w-1/2 space-y-4 md:space-y-6 text-white">
                <p className="text-sm sm:text-base md:text-xl leading-6 md:leading-7">
                  We are reshaping how people save! Every year, thousands of
                  people have new dream – To buy a new car or a new house or to
                  even execute a special project.
                </p>
                <p className="text-sm sm:text-base md:text-xl leading-6 md:leading-7">
                  We have seen that people who save alone don’t usually meet
                  this target. But with EqualCash, you save with commitment with
                  friends, families, colleagues and others to meet your target
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;
