@import "tailwindcss";
@import "tailwindcss";

@font-face {
  font-family: Nohemi;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("./assets/fonts/Nohemi-ExtraLight-BF6438cc581502c.woff") format("woff");
}

@font-face {
  font-family: Nohemi;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("./assets/fonts/Nohemi-Regular-BF6438cc579d934.woff") format("woff");
}

@font-face {
  font-family: Nohemi;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("./assets/fonts/Nohemi-Medium-BF6438cc57ddecd.woff") format("woff");
}

@font-face {
  font-family: Nohemi;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("./assets/fonts/Nohemi-Bold-BF6438cc577b524.woff") format("woff");
}

 :root {
  font-family: Nohemi, sans-serif;
} 


body {
  margin: 0;
  padding: 0;
  border: 0;
  scroll-behavior: smooth;
}

body::-webkit-scrollbar {
  width: 8px;
}


body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgb(228, 228, 228);
  border-radius: 30px;
}

body::-webkit-scrollbar-thumb {
  background: rgb(228, 228, 228);
  border-radius: 10px;
}

body::-webkit-scrollbar-thumb:hover {
  background: rgb(228, 228, 228);
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
