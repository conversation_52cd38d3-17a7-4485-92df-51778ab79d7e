import { IoIosWarning } from "react-icons/io";
import { useEffect } from "react";

export default function CareerPage() {
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);
  return (
    <main className="min-h-screen bg-[#EDE8F1] py-8 md:py-10 lg:py-12">
      <section className="flex flex-col items-center pt-4 px-4 md:px-6">
        <h1 className="text-3xl md:text-4xl lg:text-[44px] font-bold text-center">
          Careers
        </h1>
        <p className="text-gray-600 max-w-xl text-center text-sm md:text-base">
          Our mission at Equalcash is to help people save with commitment and
          passion in order to meet their financial goals!
        </p>
      </section>
      <div className="max-w-2xl mx-auto p-6 space-y-8">
        <div className="bg-white rounded-3xl flex flex-col items-center shadow-sm p-14 h-[300px]">
          <div>
            <IoIosWarning className=" size-28 text-[#B0B0B0]" />
          </div>
          <p className="text-gray-600 max-w-md text-center text-sm md:text-[20px]">
            No recent openings at this time. Please check back later!
          </p>
        </div>
      </div>
    </main>
  );
}
