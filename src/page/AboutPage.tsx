import { useEffect } from "react";
import AboutBanner1 from "../assets/about-1.webp";
import AboutBanner2 from "../assets/about-2.webp";
import DownloadApp from "../components/DownloadApp";
import FAQSection from "../components/FAQ";
import TeamProfiles from "../components/Teams";
import whyIcons from "../assets/why-icon.svg";
import TestimonialSlider from "../components/Testimonial";
import { faqs } from "../mock";

export default function AboutPage() {
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const stats = [
    { number: "1k+", label: "Active users" },
    { number: "100+", label: "Companies" },
    { number: "50+", label: "Reviews" },
    { number: "6+", label: "Countries" },
  ];

  return (
    <main className="min-h-screen bg-[#EDE8F1] py-8 md:py-12 lg:py-16">
      <section className="flex flex-col items-center pt-4 px-4 md:px-6">
        <h1 className="text-3xl md:text-4xl lg:text-[44px] font-bold text-center">
          Our Mission
        </h1>
        <p className="text-gray-600 max-w-xl text-center text-sm md:text-base">
          Our mission at Equalcash is to help people save with commitment and
          passion in order to meet their financial goals!
        </p>
      </section>
      <section className="flex flex-col lg:flex-row justify-center gap-8 lg:gap-24 max-w-7xl mx-auto items-center py-12 md:py-16 lg:py-44 px-4 md:px-6">
        <div className="w-full max-w-xl space-y-6 md:space-y-12">
          <div className="flex flex-row items-center md:items-start gap-3 sm:gap-6">
            <img src={whyIcons} alt="icon" className="w-5 sm:w-6 md:w-auto" />
            <p className="text-sm sm:text-base text-gray-700 leading-relaxed max-w-sm">
              We are reshaping how people save! Every year, thousands of people
              have new dream – To buy a new car or a new house or to even
              execute a special project.
            </p>
          </div>
          <img src={AboutBanner1} alt="banner" className="w-full" />
        </div>
        <div className="w-full max-w-xl space-y-6 md:space-y-12">
          <img src={AboutBanner2} alt="banner" className="w-full" />
          <div className="flex flex-row items-center md:items-start gap-3 sm:gap-6">
            <img src={whyIcons} alt="icon" className="w-5 sm:w-6 md:w-auto" />
            <p className="text-sm sm:text-base text-gray-700 leading-relaxed max-w-sm">
              We have seen that people who save alone don’t usually meet this
              target. But with EqualCash, you save with commitment with friends,
              families, colleagues and others to meet your target
            </p>
          </div>
        </div>
      </section>
      <section className="">
        <div className="p-4 md:p-6 max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-8 lg:gap-12">
            <div className="space-y-3">
              <div className="flex items-center gap-4">
                <div className="w-16 md:w-24 h-[2px] bg-[#545454]"></div>
                <p className="text-sm md:text-base">ABOUT US</p>
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-[44px] font-bold">
                Our Story
              </h2>
            </div>

            <div className="text-gray-600 text-sm md:text-base space-y-4 lg:max-w-3xl">
              <p>
                Equal Cash is a form of unique and combined peer-to-peer banking
                and peer-to-peer lending. Equal Cash Technology APP is simply
                the automation of the "Ajo" or "Esusu," commonly practiced in
                Nigeria and other Sub-Saharan Africa and rotating saving and
                credit association - "ROSCA" in East Africa.
              </p>

              <p>
                Equal Cash will support and impact the global development
                programme of the United Nation to drive the sustainable
                development goals especially in Sub-saharan Africa.
                Specifically, it will support the development of the Financial
                inclusion Action Plan (FIAP) and the Global Partnership for
                Financial Inclusion (GPFI) to support both Group of 20 member
                countries and interested non-Group of 20 member countries on
                innovative Financial inclusion.
              </p>

              <p>
                Equal Cash App will provide users with three distinct benefits
                referred to as "3 in 1" benefit. Equal Cash will be via is:
              </p>

              <ul className="ml-4">
                <li className="list-disc">Saving tool</li>
                <li className="list-disc">Down Payment Cash</li>
                <li className="list-disc">Zero interest peer to peer loan</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-[#411567] py-12 md:py-16 lg:py-20 mt-12 md:mt-16">
          <div className="max-w-4xl mx-auto px-4 md:px-6">
            <div className="grid grid-cols-2 md:flex md:justify-between items-center gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-white text-3xl md:text-4xl lg:text-5xl font-bold mb-1">
                    {stat.number}
                  </div>
                  <div className="text-purple-200 text-sm md:text-md">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      <section className="mt-16 md:mt-24 lg:mt-32 px-4 md:px-6">
        <div className="space-y-2 text-center mb-12 md:mb-16 lg:mb-20">
          <article className="flex justify-center items-center gap-2">
            <div className="w-16 md:w-24 h-[2px] bg-[#545454]"></div>
            <span className="text-sm md:text-base">OUR FOUNDERS</span>
            <div className="w-16 md:w-24 h-[2px] bg-[#545454]"></div>
          </article>
          <h2 className="text-3xl md:text-4xl lg:text-[44px] font-bold">
            Meet The Team
          </h2>
        </div>
        <TeamProfiles />
      </section>
      <TestimonialSlider />
      <section className="mt-18 sm:mt-16 lg:mt-10">
        <FAQSection data={faqs} />
      </section>
      <DownloadApp />
    </main>
  );
}
