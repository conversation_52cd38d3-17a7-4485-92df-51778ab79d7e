import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FaStar } from "react-icons/fa";
import Banner from "../assets/banner-1.webp";
import BgImage from "../assets/bg-image.svg";
import HowItWorks from "../components/HowWorks";
import WhyChooseUs from "../components/WhyUs";
import AboutUsSection from "../components/AboutUS";
import TestimonialSlider from "../components/Testimonial";
import FAQSection from "../components/FAQ";
import DownloadApp from "../components/DownloadApp";
import { faqs } from "../mock";
import React, { useEffect, useState } from "react";
import QRDownloadModal from "../components/QRCodeModal";

const Home = () => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  React.useEffect(() => {
    document.documentElement.style.scrollBehavior = "smooth";

    return () => {
      document.documentElement.style.scrollBehavior = "auto";
    };
  }, []);

  return (
    <div className="min-h-screen bg-[#EDE8F1]">
      <div className="py-12 px-4 sm:px-16 flex flex-col lg:flex-row items-start lg:items-center gap-8 sm:gap-12">
        <div className="lg:w-full space-y-6 sm:space-y-8 order-2 sm:order-1">
          <div className="inline-flex items-center space-x-2  py-2 rounded-full relative">
            <div className="w-[26px] h-[26px] relative">
              <span className="absolute inset-0 animate-ping">
                <svg viewBox="0 0 26 26" className="w-full h-full">
                  <circle cx="13" cy="13" r="13" className="fill-[#C6B7D3]" />
                </svg>
              </span>
              <span className="absolute inset-0">
                <svg viewBox="0 0 26 26" className="w-full h-full">
                  <circle cx="13" cy="13" r="6" className="fill-[#5C2F89]" />
                </svg>
              </span>
            </div>
            <p className="text-sm">
              Fast track your journey to financial freedom with our 3-in-1
              solution
            </p>
          </div>

          <h2 className="text-3xl sm:text-4xl lg:text-[46px] font-bold text-gray-900">
            A combined peer-to-peer saving platform.
          </h2>

          <p className="text-xl text-gray-600">
            We help people save with commitment and passion in order to meet
            your financial goals!
          </p>

          <div className="flex flex-col items-start lg:items-center md:flex-row gap-8">
            <button
              className="flex items-center space-x-2 bg-[#4B1C74] text-white px-6 py-3 cursor-pointer rounded-lg hover:bg-purple-800 transition-colors"
              onClick={() => setIsOpen(true)}
            >
              <FaGooglePlay size={20} />
              <FaApple size={20} />
              <span>Download App</span>
            </button>

            <div className="space-y-2">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <FaStar
                    key={star}
                    size={16}
                    className="fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
              <span className="text-gray-600">
                4.8/5 - Available on iOS & Android
              </span>
            </div>
          </div>
        </div>

        <div className="lg:w-full relative order-1 sm:order-2">
          <div className="relative">
            <div className="absolute inset-0 overflow-hidden z-0">
              <img
                src={BgImage}
                alt="Background pattern"
                className="h-full w-full object-cover"
              />
            </div>
            <img
              src={Banner}
              alt="Mobile app mockup"
              className="relative z-10 w-full h-auto max-w-[400px] sm:max-w-[600px] mx-auto"
            />
          </div>
        </div>
      </div>
      <HowItWorks />
      <WhyChooseUs />
      <AboutUsSection />
      <TestimonialSlider />
      <section className="mt-10 sm:mt-12 lg:mt-16">
        <FAQSection data={faqs} />
      </section>
      <DownloadApp />
      <QRDownloadModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
};

export default Home;
