import { CalendarDays } from "lucide-react";
import { useEffect } from "react";

export default function TermUsePage() {
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <main className="min-h-screen bg-[#EDE8F1] py-8 md:py-12 lg:py-14">
      <section className="flex flex-col items-center pt-4 px-4 md:px-6">
        <h1 className="text-3xl md:text-4xl lg:text-[44px] font-bold text-center">
          Terms of Use
        </h1>
        <p className="text-gray-600 max-w-xl text-center text-sm md:text-base flex items-center gap-2">
         <CalendarDays className="w-4 h-4 text-gray-600" /> Version Date: 15th April 2025 (Version 1.0)
        </p>
      </section>
      <div className="max-w-6xl mx-auto p-6 md:p-8 space-y-6">
        <section className="prose prose-gray max-w-none">
          <p>
            Equal Cash Technology Ltd (Equal Cash) operates a digital Rotating Savings and Credit Association (ROSCA) platform. The Equal Cash mobile application offers two schemes: (a) Open Company, wherein members are matched by the platform and, (b) Close Company, wherein members are invited by a CEO who knows the participants.
          </p>
          <p>
            Participation relies on mutual trust among members. By downloading the App, opening an account, and joining any Company, you agree to these core obligations and liability provisions:
          </p>
          <ol className="list-decimal pl-5 space-y-2 mt-10">
            <li>
              <strong>Voluntary Participation:</strong> You join a Company of your own free will and are not coerced by the CEO, Equal Cash, or other members.
            </li>
            <li>
              <strong>Proof of Income:</strong> You confirm that you are gainfully employed or have a legitimate source of income and have supplied supporting bank‐statement evidence during Know-Your-Customer (KYC) screening.
            </li>
            <li>
              <strong>Funding Commitment:</strong> You shall maintain sufficient funds in your linked account or card so scheduled contributions (Cash Drops) are debited on time for the benefit of other members.
            </li>
            <li>
              <strong>No Fraudulent Intent:</strong> You must not enter a Company in order to withdraw before completion or to default after receiving your Cash Bucket. Such conduct constitutes fraud.
            </li>
            <li>
              <strong>Dispute Resolution:</strong> You will accept the decision of an authorised Equal Cash representative on any dispute arising within your Company.
            </li>
            <li>
              <strong>Legal Action on Misconduct:</strong> If your actions are deemed fraudulent or misleading, you may be sued jointly or severally by Equal Cash and affected members. You will bear all costs awarded by a competent court in our jurisdictions of operation.
            </li>
            <li>
              <strong>Binding Effect:</strong> These Terms take effect on the date your Company's schedule commences and remain binding until the Scheme ends or your obligations are fully discharged.
            </li>
            <li>
              <strong>Warranties, Indemnity, and Liability:</strong> Equal Cash provides the App "as is" and disclaims all implied warranties. Equal Cash is not liable for indirect or consequential loss arising from member default. You agree to indemnify Equal Cash against claims, losses, or expenses resulting from your breach of these Terms or applicable law.
            </li>
            <li>
              <strong>Integration, Severability, Waiver:</strong> These Terms summarize key duties found in the full Equal Cash User Terms and Conditions. If a clause is held unenforceable, the remainder survives. A failure to enforce any clause is not a waiver of future enforcement.
            </li>
            <li>
              <strong>Relationship to Full Terms and Conditions:</strong> This summary does not override the comprehensive Equal Cash User Terms and Conditions. If any provision here conflicts with the full Terms and Conditions, the latter shall prevail to the extent of the inconsistency.
            </li>
          </ol>
          <p className="mt-6">
            <em>Note: Continued use of the App constitutes acceptance of this Terms of Use.</em>
          </p>
        </section>
      </div>
    </main>
  );
}
