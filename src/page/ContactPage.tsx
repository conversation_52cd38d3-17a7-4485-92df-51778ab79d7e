import { Mail, MapPin, Phone } from "lucide-react";
import { useEffect } from "react";

export default function ContactPage() {
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);
  return (
    <main className="min-h-screen bg-[#EDE8F1] py-8 md:py-12 lg:py-24">
      <section className="flex flex-col items-center pt-4 px-4 md:px-6 space-y-4 mb-8">
        <h1 className="text-3xl md:text-4xl lg:text-[44px] font-bold text-center">
          Contact
        </h1>
        <p className="text-gray-600 max-w-xl text-center text-sm md:text-base">
          Any questions or remarks? Just write us a message
        </p>
      </section>
      <div className="max-w-4xl mx-auto p-6 space-y-8">
        <div className="bg-white rounded-4xl shadow-sm p-12">
          <h2 className="text-3xl font-bold mb-6">Get In Touch</h2>
          <form className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <input
                type="text"
                placeholder="Your Name"
                className="p-3 rounded-lg bg-[#EDE8F1] border-0 w-full outline-none"
              />
              <input
                type="email"
                placeholder="Your Email"
                className="p-3 rounded-lg bg-[#EDE8F1] border-0 w-full outline-none"
              />
            </div>
            <input
              type="tel"
              placeholder="Your Phone Number"
              className="p-3 rounded-lg bg-[#EDE8F1] border-0 w-full outline-none"
            />
            <textarea
              placeholder="Your Message"
              rows={4}
              className="p-3 rounded-lg bg-[#EDE8F1] border-0 w-full resize-none outline-none"
            />
          </form>
          <button
            className="max-w-md bg-[#4B1C74] text-white px-4 py-2 font-bold cursor-pointer rounded-md hover:bg-purple-800 my-4"
            // onClick={() => setIsOpen(true)}
          >
            Submit Message ⟶
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 items-start gap-4">
          <div className="bg-white rounded-2xl shadow-sm h-[200px] px-6 py-4">
            <div className="flex flex-col items-start space-y-4">
              <MapPin className="text-[#471771]" size={50} />
              <div className="font-semibold space-y-2">
                <p className="text-sm text-gray-500 font-medium">
                  Office Address:
                </p>
                <p className="text-sm">6709 Green Haven Road,</p>
                <p className="text-sm">Lanham, MD, 20706, United States.</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm h-[200px] px-6 py-8">
            <div className="flex flex-col items-start space-y-4">
              <Mail className="text-[#471771]" size={50} />
              <div className="font-semibold space-y-2">
                <p className="text-sm text-gray-500 font-medium">
                  Email Address:
                </p>
                <p className="text-sm"><EMAIL></p>
                <p className="text-sm">&nbsp;</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm h-[200px] px-6 py-8">
            <div className="flex flex-col items-start space-y-4">
              <Phone className="text-[#471771]" size={50} />
              <div className="font-semibold space-y-2">
                <p className="text-sm text-gray-500 font-medium">
                  Phone Number:
                </p>
                <p className="text-sm">******-408-5015</p>
                <p className="text-sm">&nbsp;</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
