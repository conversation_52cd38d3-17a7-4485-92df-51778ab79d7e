import { useEffect } from "react";
import DownloadApp from "../components/DownloadApp";
import FAQSection from "../components/FAQ";
import TestimonialSlider from "../components/Testimonial";
import { faqData } from "../mock";

export default function FAQPage() {
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, [])
  return (
    <main className="min-h-screen bg-[#EDE8F1] py-8">
      <section className="flex flex-col items-center pt-4 px-4 md:px-6">
        <FAQSection data={faqData} subtitle={false} />
      </section>
      <TestimonialSlider />
      <DownloadApp />
    </main>
  );
}
